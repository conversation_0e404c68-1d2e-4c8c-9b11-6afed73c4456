<script setup>
const emit = defineEmits(['close']);

const state = reactive({
  mode: 'data-builder',
});

function publishWidget() {
  logger.log('PUBLISH WIDGET');
}
</script>

<template>
  <BiLayout @close="emit('close')">
    <template #left-content>
      <BiQueryBuilder v-if="state.mode === 'data-builder'" :selected-table="{ name: 'Progress History', columns: [{ name: 'Progress', type: 'number', operator: [{ name: 'sum' }] }, { name: 'Date', type: 'date' }] }" />
      <BiChartBuilder v-else-if="state.mode === 'chart-builder'" />
    </template>
    <template #right-content>
      <BiDataPreview
        v-if="state.mode === 'data-builder'"
        @continue="state.mode = 'chart-builder'"
      />
      <BiWidgetPreview
        v-else-if="state.mode === 'chart-builder'"
        @go-back="state.mode = 'data-builder'"
        @continue="publishWidget"
      />
    </template>
  </BiLayout>
</template>
