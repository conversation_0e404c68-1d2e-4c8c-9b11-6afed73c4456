<script setup>
defineProps({
  selectedTable: {
    type: Object,
    default: () => ({}),
  },
});
</script>

<template>
  <div class="w-full h-screen overflow-auto">
    <div class="text-sm text-gray-700 font-semibold flex items-center gap-1 mb-4">
      <IconHawkDatabaseTwo class="size-4" /> {{ selectedTable.name }}
    </div>
    <div>
      <bi-query-builder-columns-dropdown :tables="[selectedTable]" />
    </div>
  </div>
</template>
